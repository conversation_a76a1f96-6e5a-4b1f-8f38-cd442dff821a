#!/usr/bin/env python3
"""
Test script to verify HMM Chatbot installation and basic functionality.

This script performs basic checks to ensure all dependencies are installed
and the chatbot can be initialized properly.
"""

import sys
import os
from pathlib import Path
import importlib
import traceback

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test if all required packages can be imported."""
    print("🔍 Testing imports...")
    
    required_packages = [
        "torch",
        "transformers", 
        "speechbrain",
        "soundfile",
        "librosa",
        "numpy",
        "scipy",
        "langgraph",
        "langchain",
        "pydantic",
        "rich",
        "typer"
    ]
    
    optional_packages = [
        "pyaudio",  # Might not be available on all systems
        "fastapi",
        "uvicorn"
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"  ✅ {package}")
        except ImportError as e:
            print(f"  ❌ {package}: {e}")
            failed_imports.append(package)
    
    for package in optional_packages:
        try:
            importlib.import_module(package)
            print(f"  ✅ {package} (optional)")
        except ImportError:
            print(f"  ⚠️  {package} (optional): Not available")
    
    if failed_imports:
        print(f"\n❌ Failed to import required packages: {failed_imports}")
        print("Please install missing packages with: pip install -e .")
        return False
    
    print("✅ All required imports successful!")
    return True


def test_torch_cuda():
    """Test CUDA availability."""
    print("\n🔍 Testing CUDA availability...")
    
    try:
        import torch
        
        print(f"  PyTorch version: {torch.__version__}")
        print(f"  CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"  CUDA version: {torch.version.cuda}")
            print(f"  CUDA devices: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"    Device {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("  ⚠️  CUDA not available, will use CPU")
        
        return True
    except Exception as e:
        print(f"  ❌ Error testing CUDA: {e}")
        return False


def test_audio_system():
    """Test audio system availability."""
    print("\n🔍 Testing audio system...")
    
    try:
        import pyaudio
        
        # Try to initialize PyAudio
        audio = pyaudio.PyAudio()
        
        # Get device info
        device_count = audio.get_device_count()
        print(f"  Audio devices found: {device_count}")
        
        # Find default input device
        try:
            default_input = audio.get_default_input_device_info()
            print(f"  Default input device: {default_input['name']}")
        except:
            print("  ⚠️  No default input device found")
        
        # Find default output device
        try:
            default_output = audio.get_default_output_device_info()
            print(f"  Default output device: {default_output['name']}")
        except:
            print("  ⚠️  No default output device found")
        
        audio.terminate()
        print("  ✅ Audio system working")
        return True
        
    except ImportError:
        print("  ⚠️  PyAudio not available - voice features will not work")
        print("  Install with: pip install pyaudio")
        return False
    except Exception as e:
        print(f"  ❌ Audio system error: {e}")
        return False


def test_chatbot_config():
    """Test chatbot configuration."""
    print("\n🔍 Testing chatbot configuration...")
    
    try:
        from chatbot import ChatbotConfig
        
        # Test default config
        config = ChatbotConfig()
        print(f"  ✅ Default config created")
        print(f"    LLM model: {config.llm_config.model_name}")
        print(f"    STT model: {config.stt_config.model_name}")
        print(f"    Emotion model: {config.emotion_config.model_name}")
        print(f"    TTS model: {config.tts_config.model_name}")
        print(f"    Cache dir: {config.cache_dir}")
        
        # Test environment config
        config_env = ChatbotConfig.from_env()
        print(f"  ✅ Environment config created")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Config error: {e}")
        traceback.print_exc()
        return False


def test_model_manager():
    """Test model manager initialization."""
    print("\n🔍 Testing model manager...")
    
    try:
        from chatbot import ModelManager, ChatbotConfig
        
        config = ChatbotConfig()
        # Use CPU to avoid CUDA issues during testing
        config.llm_config.device = "cpu"
        config.stt_config.device = "cpu"
        config.emotion_config.device = "cpu"
        config.tts_config.device = "cpu"
        
        manager = ModelManager(config)
        print(f"  ✅ Model manager created")
        print(f"    Models loaded: {manager.is_loaded}")
        
        # Note: We don't actually load models here as it would take too long
        # and require significant resources
        
        return True
        
    except Exception as e:
        print(f"  ❌ Model manager error: {e}")
        traceback.print_exc()
        return False


def test_chatbot_creation():
    """Test chatbot creation without loading models."""
    print("\n🔍 Testing chatbot creation...")
    
    try:
        from chatbot import MultiModalChatbot, ChatbotConfig
        
        config = ChatbotConfig()
        # Use CPU to avoid CUDA issues
        config.llm_config.device = "cpu"
        config.stt_config.device = "cpu"
        config.emotion_config.device = "cpu"
        config.tts_config.device = "cpu"
        
        chatbot = MultiModalChatbot(config)
        print(f"  ✅ Chatbot created")
        print(f"    Conversation ID: {chatbot.conversation_id}")
        print(f"    Initialized: {chatbot._initialized}")
        
        # Test configuration access
        stats = chatbot.get_stats()
        print(f"    Stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Chatbot creation error: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 HMM Chatbot Installation Test")
    print("=" * 50)
    
    tests = [
        ("Package Imports", test_imports),
        ("CUDA Support", test_torch_cuda),
        ("Audio System", test_audio_system),
        ("Chatbot Config", test_chatbot_config),
        ("Model Manager", test_model_manager),
        ("Chatbot Creation", test_chatbot_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your installation looks good.")
        print("\nNext steps:")
        print("1. Run: python main.py interactive")
        print("2. Or try: python examples/simple_usage.py")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the errors above.")
        print("\nCommon solutions:")
        print("1. Install missing packages: pip install -e .")
        print("2. Install audio dependencies: pip install pyaudio")
        print("3. Check CUDA installation if you want GPU support")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
