"""
Multi-modal AI Chatbot with Voice and Emotion Recognition

This package provides a comprehensive chatbot that can:
- Respond to any topic using Meta-Llama-3.1-8B-Instruct
- Process voice input using OpenAI Whisper
- Recognize emotions using SpeechBrain
- Generate natural voice responses using Parler TTS
- Orchestrate conversations using LangGraph
"""

from .core import MultiModalChatbot
from .models import ModelManager
from .config import ChatbotConfig

__version__ = "0.1.0"
__all__ = ["MultiModalChatbot", "ModelManager", "ChatbotConfig"]
