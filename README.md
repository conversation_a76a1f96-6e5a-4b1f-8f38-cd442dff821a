# 🤖 HMM Chatbot - Multi-Modal AI Assistant

A comprehensive multi-modal AI chatbot that responds to any topic with both text and voice responses, featuring emotion recognition and human-like conversation capabilities.

## ✨ Features

- **🗣️ Voice Input**: Speech-to-text using OpenAI Whisper Large V3
- **🎯 Emotion Recognition**: Real-time emotion detection using SpeechBrain
- **🧠 Intelligent Responses**: Powered by Mistral-7B-Instruct-v0.2
- **🔊 Voice Output**: Natural text-to-speech using Parler TTS
- **🔄 LangGraph Orchestration**: Sophisticated conversation flow management
- **💭 Context Awareness**: Maintains conversation history and emotional context
- **🎛️ Flexible Interface**: Command-line, programmatic, and interactive modes

## 🏗️ Architecture

The chatbot uses **LangGraph** to orchestrate a sophisticated workflow:

```
Audio Input → STT → Emotion Detection → LLM Response → TTS → Audio Output
     ↓           ↓         ↓              ↓         ↓         ↓
  Recording   Whisper  SpeechBrain    Mistral-7B   Parler   Playback
```

### Models Used

- **LLM**: `mistralai/Mistral-7B-Instruct-v0.2` - For intelligent responses
- **STT**: `openai/whisper-large-v3` - For speech recognition
- **Emotion**: `speechbrain/emotion-recognition-wav2vec2-IEMOCAP` - For emotion detection
- **TTS**: `parler-tts/parler-tts-mini-v0.1` - For speech synthesis

## 🚀 Quick Start

### Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd hmm
```

2. **Install dependencies**:
```bash
pip install -e .
```

3. **Install additional system dependencies** (if needed):
```bash
# On Ubuntu/Debian
sudo apt-get install portaudio19-dev python3-pyaudio

# On macOS
brew install portaudio
```

### Basic Usage

#### Command Line Interface

```bash
# Interactive text chat
python main.py interactive

# Interactive with voice enabled
python main.py interactive --voice

# Single text message
python main.py chat "Hello, how are you?"

# Voice chat
python main.py voice-chat --record

# Process audio file
python main.py voice-chat --file audio.wav
```

#### Programmatic Usage

```python
import asyncio
from chatbot import MultiModalChatbot

async def main():
    async with MultiModalChatbot() as chatbot:
        # Text chat
        result = chatbot.chat_text("Hello!")
        print(result["text_response"])

        # Voice chat
        result = chatbot.chat_voice(record_live=True)
        print(f"You said: {result['transcribed_text']}")
        print(f"Bot replied: {result['text_response']}")

        # Play audio response
        chatbot.play_response(result)

asyncio.run(main())
```

## 📖 Detailed Usage

### Text-Only Chat

```python
from chatbot import quick_chat_text

# Quick one-off response
response = quick_chat_text("What's the weather like?")
print(response)
```

### Voice Chat with Emotion Detection

```python
import asyncio
from chatbot import MultiModalChatbot

async def voice_chat_example():
    async with MultiModalChatbot() as chatbot:
        result = chatbot.chat_voice(record_live=True)

        print(f"Transcription: {result['transcribed_text']}")
        print(f"Emotion: {result['dominant_emotion']}")
        print(f"Response: {result['text_response']}")

        # Play the audio response
        if result['audio_response'] is not None:
            chatbot.play_response(result)

asyncio.run(voice_chat_example())
```

### Conversation with Context

```python
import asyncio
from chatbot import MultiModalChatbot

async def contextual_chat():
    async with MultiModalChatbot() as chatbot:
        # First turn
        result1 = chatbot.chat_text("My name is Alice")
        print(f"Bot: {result1['text_response']}")

        # Second turn - bot remembers the name
        result2 = chatbot.chat_text("What's my name?")
        print(f"Bot: {result2['text_response']}")

        # View conversation history
        history = chatbot.get_conversation_history()
        print(f"Total turns: {len(history)}")

asyncio.run(contextual_chat())
```

## ⚙️ Configuration

### Environment Variables

```bash
# Model configuration
export HMM_LLM_MODEL="mistralai/Mistral-7B-Instruct-v0.2"
export HMM_STT_MODEL="openai/whisper-large-v3"
export HMM_EMOTION_MODEL="speechbrain/emotion-recognition-wav2vec2-IEMOCAP"
export HMM_TTS_MODEL="parler-tts/parler-tts-mini-v0.1"

# Device configuration
export HMM_DEVICE="cuda"  # or "cpu" or "auto"

# Cache directory
export HMM_CACHE_DIR="/path/to/cache"

# API configuration (if using API mode)
export HMM_API_HOST="localhost"
export HMM_API_PORT="8000"
```

### Programmatic Configuration

```python
from chatbot import ChatbotConfig, MultiModalChatbot

# Create custom configuration
config = ChatbotConfig()
config.temperature = 0.9  # More creative responses
config.max_tokens = 200   # Longer responses
config.system_prompt = "You are a helpful assistant that speaks like Shakespeare."

# Use custom config
async with MultiModalChatbot(config) as chatbot:
    result = chatbot.chat_text("Tell me about the weather")
    print(result["text_response"])
```

## 🎛️ Advanced Features

### Audio Processing

```python
import asyncio
from chatbot import MultiModalChatbot

async def audio_processing_example():
    async with MultiModalChatbot() as chatbot:
        # Process audio file
        result = chatbot.chat_voice(audio_file="input.wav", record_live=False)

        # Save response audio
        chatbot.save_response_audio(result, "response.wav")

        # Get audio processing stats
        stats = chatbot.get_stats()
        print(f"Emotion distribution: {stats['emotion_distribution']}")

asyncio.run(audio_processing_example())
```

### Emotion-Aware Responses

The chatbot automatically detects emotions from voice input and adjusts its responses accordingly:

- **Happy**: Responds with enthusiasm and positivity
- **Sad**: Responds with empathy and gentle support
- **Angry**: Responds calmly and tries to be helpful
- **Neutral**: Responds naturally and helpfully

### Conversation Management

```python
import asyncio
from chatbot import MultiModalChatbot

async def conversation_management():
    async with MultiModalChatbot() as chatbot:
        # Chat normally
        chatbot.chat_text("Hello!")

        # Get conversation statistics
        stats = chatbot.get_stats()
        print(f"Total turns: {stats['total_turns']}")
        print(f"Emotions detected: {stats['emotion_distribution']}")

        # Clear conversation history
        chatbot.clear_conversation()

        # Update configuration on the fly
        chatbot.update_config(temperature=0.5, max_tokens=100)

asyncio.run(conversation_management())
```

## 🔧 Development

### Project Structure

```
hmm/
├── src/chatbot/           # Main chatbot package
│   ├── __init__.py       # Package exports
│   ├── core.py           # Main chatbot class
│   ├── models.py         # Model wrappers
│   ├── graph.py          # LangGraph workflow
│   ├── audio_handler.py  # Audio processing
│   └── config.py         # Configuration
├── examples/             # Usage examples
│   ├── demo.py          # Comprehensive demo
│   └── simple_usage.py  # Basic examples
├── main.py              # CLI entry point
├── pyproject.toml       # Project configuration
└── README.md           # This file
```

### Running Examples

```bash
# Run the comprehensive demo
python examples/demo.py

# Run simple usage examples
python examples/simple_usage.py
```

### Testing

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests (when available)
pytest

# Code formatting
black src/ examples/ main.py
isort src/ examples/ main.py

# Type checking
mypy src/
```

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   ```python
   # Use CPU instead
   config = ChatbotConfig()
   config.llm_config.device = "cpu"
   config.stt_config.device = "cpu"
   config.emotion_config.device = "cpu"
   config.tts_config.device = "cpu"
   ```

2. **Audio Device Not Found**:
   ```bash
   # Install audio dependencies
   pip install pyaudio

   # On Linux, you might need:
   sudo apt-get install portaudio19-dev
   ```

3. **Model Download Issues**:
   ```python
   # Set custom cache directory
   config = ChatbotConfig()
   config.cache_dir = "/path/to/large/storage"
   ```

4. **Memory Issues with Large Models**:
   ```python
   # Use quantization
   config = ChatbotConfig()
   config.llm_config.load_in_8bit = True
   # or
   config.llm_config.load_in_4bit = True
   ```

### Performance Optimization

1. **Model Loading**: Models are loaded once and cached for the session
2. **Audio Processing**: Real-time processing with voice activity detection
3. **Memory Management**: Automatic cleanup and CUDA cache clearing
4. **Quantization**: Support for 8-bit and 4-bit model quantization

## 📊 Performance Metrics

### Model Sizes and Requirements

| Model | Size | VRAM (FP16) | VRAM (8-bit) | CPU RAM |
|-------|------|-------------|--------------|---------|
| Mistral-7B | ~16GB | ~16GB | ~8GB | ~32GB |
| Whisper-Large-V3 | ~3GB | ~3GB | ~1.5GB | ~6GB |
| SpeechBrain Emotion | ~400MB | ~400MB | ~200MB | ~800MB |
| Parler TTS Mini | ~1GB | ~1GB | ~500MB | ~2GB |

### Latency Benchmarks

- **Text Response**: 1-3 seconds (depending on hardware)
- **Speech Recognition**: 0.5-2 seconds (depending on audio length)
- **Emotion Detection**: 0.1-0.5 seconds
- **Speech Synthesis**: 1-3 seconds (depending on text length)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow PEP 8 style guidelines
- Add type hints to all functions
- Include docstrings for all public methods
- Write tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Meta AI** for the Mistral-7B model
- **OpenAI** for the Whisper model
- **SpeechBrain** for emotion recognition
- **Parler AI** for text-to-speech
- **LangChain/LangGraph** for workflow orchestration

## 🔮 Future Enhancements

- [ ] Web interface with real-time voice chat
- [ ] Multi-language support
- [ ] Custom voice cloning
- [ ] Integration with external APIs
- [ ] Conversation analytics dashboard
- [ ] Plugin system for custom models
- [ ] Mobile app support
- [ ] Real-time streaming responses

## 📞 Support

If you encounter any issues or have questions:

1. Check the [troubleshooting section](#-troubleshooting)
2. Look through existing [GitHub issues](https://github.com/your-repo/issues)
3. Create a new issue with detailed information about your problem

---

**Made with ❤️ for the AI community**