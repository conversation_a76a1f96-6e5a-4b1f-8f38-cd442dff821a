# HMM Chatbot Makefile
# Provides common development and deployment tasks

.PHONY: help install install-dev test test-install clean format lint type-check run demo docs

# Default target
help:
	@echo "🤖 HMM Chatbot - Available Commands:"
	@echo ""
	@echo "Installation (pip):"
	@echo "  install      - Install the package and dependencies"
	@echo "  install-dev  - Install with development dependencies"
	@echo ""
	@echo "Installation (uv - faster):"
	@echo "  install-uv     - Install with uv"
	@echo "  install-dev-uv - Install with uv (dev dependencies)"
	@echo "  setup-uv       - Setup complete uv environment"
	@echo ""
	@echo "Testing:"
	@echo "  test-install - Test if installation is working"
	@echo ""
	@echo "Development:"
	@echo "  format       - Format code with black and isort"
	@echo "  lint         - Run flake8 linting"
	@echo "  type-check   - Run mypy type checking"
	@echo "  test         - Run tests (when available)"
	@echo "  clean        - Clean up cache and temporary files"
	@echo ""
	@echo "Usage (standard):"
	@echo "  run          - Run interactive chatbot"
	@echo "  run-voice    - Run interactive chatbot with voice"
	@echo "  demo         - Run demo script"
	@echo "  examples     - Run simple usage examples"
	@echo ""
	@echo "Usage (with uv):"
	@echo "  run-uv       - Run interactive chatbot with uv"
	@echo "  run-voice-uv - Run interactive chatbot with voice (uv)"
	@echo "  demo-uv      - Run demo script with uv"
	@echo "  examples-uv  - Run simple usage examples with uv"
	@echo ""
	@echo "Documentation:"
	@echo "  docs         - Generate documentation (if available)"

# Installation targets
install:
	@echo "📦 Installing HMM Chatbot..."
	pip install -e .

install-dev:
	@echo "📦 Installing HMM Chatbot with development dependencies..."
	pip install -e ".[dev]"

# UV installation targets
install-uv:
	@echo "📦 Installing HMM Chatbot with uv..."
	uv pip install -e .

install-dev-uv:
	@echo "📦 Installing HMM Chatbot with uv (dev dependencies)..."
	uv pip install -e ".[dev]"

setup-uv:
	@echo "🚀 Setting up project with uv..."
	uv venv
	uv pip install -e ".[dev]"

test-install:
	@echo "🔍 Testing installation..."
	python test_installation.py

# Development targets
format:
	@echo "🎨 Formatting code..."
	black src/ examples/ main.py test_installation.py
	isort src/ examples/ main.py test_installation.py

lint:
	@echo "🔍 Running linting..."
	flake8 src/ examples/ main.py test_installation.py

type-check:
	@echo "🔍 Running type checking..."
	mypy src/

test:
	@echo "🧪 Running tests..."
	pytest tests/ -v

clean:
	@echo "🧹 Cleaning up..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -name "*.pyc" -delete 2>/dev/null || true
	find . -name "*.pyo" -delete 2>/dev/null || true
	find . -name "*~" -delete 2>/dev/null || true

# Usage targets
run:
	@echo "🚀 Starting interactive chatbot..."
	python main.py interactive

run-voice:
	@echo "🚀 Starting interactive chatbot with voice..."
	python main.py interactive --voice

demo:
	@echo "🎬 Running demo..."
	python examples/demo.py

examples:
	@echo "📚 Running simple usage examples..."
	python examples/simple_usage.py

# UV usage targets
run-uv:
	@echo "🚀 Starting interactive chatbot with uv..."
	uv run python main.py interactive

run-voice-uv:
	@echo "🚀 Starting interactive chatbot with voice (uv)..."
	uv run python main.py interactive --voice

demo-uv:
	@echo "🎬 Running demo with uv..."
	uv run python examples/demo.py

examples-uv:
	@echo "📚 Running simple usage examples with uv..."
	uv run python examples/simple_usage.py

# Quick commands
chat:
	@echo "💬 Quick chat mode..."
	@echo "Usage: make chat MESSAGE=\"Your message here\""
	@if [ -z "$(MESSAGE)" ]; then \
		echo "Please provide a message: make chat MESSAGE=\"Hello!\""; \
	else \
		python main.py chat "$(MESSAGE)"; \
	fi

voice-chat:
	@echo "🎤 Voice chat mode..."
	python main.py voice-chat

# Development workflow
dev-setup: install-dev
	@echo "🛠️  Setting up development environment..."
	@echo "Creating .env file from template..."
	@if [ ! -f .env ]; then cp .env.example .env; fi
	@echo "✅ Development setup complete!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Edit .env file with your preferences"
	@echo "2. Run: make test-install"
	@echo "3. Run: make run"

# CI/CD targets
ci-test: install-dev test-install lint type-check
	@echo "✅ CI tests completed"

# Documentation (placeholder)
docs:
	@echo "📚 Documentation generation not yet implemented"
	@echo "For now, see README.md for documentation"

# Docker targets (if Docker support is added later)
docker-build:
	@echo "🐳 Docker support not yet implemented"

docker-run:
	@echo "🐳 Docker support not yet implemented"

# Deployment targets (placeholder)
deploy:
	@echo "🚀 Deployment not yet implemented"

# Model management
download-models:
	@echo "📥 Pre-downloading models..."
	@echo "This will download all required models to cache..."
	python -c "import asyncio; from src.chatbot import MultiModalChatbot, ChatbotConfig; asyncio.run(MultiModalChatbot(ChatbotConfig()).initialize())"

# Performance testing
benchmark:
	@echo "⚡ Performance benchmarking not yet implemented"

# Security scanning
security-scan:
	@echo "🔒 Security scanning not yet implemented"
	@echo "Consider using: pip install safety && safety check"
