"""Audio processing utilities for the chatbot."""

import pyaudio
import numpy as np
import soundfile as sf
import librosa
import threading
import queue
import time
import logging
from typing import Optional, Callable, Tuple
from pathlib import Path
import tempfile

from .config import AudioConfig

logger = logging.getLogger(__name__)


class AudioRecorder:
    """Real-time audio recorder with voice activity detection."""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.audio = pyaudio.PyAudio()
        self.stream = None
        self.recording = False
        self.audio_queue = queue.Queue()
        self.record_thread = None
        
    def start_recording(self) -> None:
        """Start recording audio."""
        if self.recording:
            return
        
        logger.info("Starting audio recording...")
        
        # Open audio stream
        self.stream = self.audio.open(
            format=pyaudio.paFloat32,
            channels=self.config.channels,
            rate=self.config.sample_rate,
            input=True,
            frames_per_buffer=self.config.chunk_size
        )
        
        self.recording = True
        self.record_thread = threading.Thread(target=self._record_loop)
        self.record_thread.start()
    
    def stop_recording(self) -> np.ndarray:
        """Stop recording and return audio data."""
        if not self.recording:
            return np.array([])
        
        logger.info("Stopping audio recording...")
        
        self.recording = False
        
        if self.record_thread:
            self.record_thread.join()
        
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
        
        # Collect all audio data
        audio_data = []
        while not self.audio_queue.empty():
            try:
                chunk = self.audio_queue.get_nowait()
                audio_data.append(chunk)
            except queue.Empty:
                break
        
        if audio_data:
            return np.concatenate(audio_data)
        else:
            return np.array([])
    
    def _record_loop(self) -> None:
        """Main recording loop."""
        while self.recording:
            try:
                data = self.stream.read(self.config.chunk_size, exception_on_overflow=False)
                audio_chunk = np.frombuffer(data, dtype=np.float32)
                self.audio_queue.put(audio_chunk)
            except Exception as e:
                logger.error(f"Error in recording loop: {e}")
                break
    
    def __del__(self):
        """Cleanup resources."""
        if self.recording:
            self.stop_recording()
        
        if hasattr(self, 'audio'):
            self.audio.terminate()


class VoiceActivityDetector:
    """Simple voice activity detection based on energy and silence."""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.silence_threshold = config.silence_threshold
        self.silence_duration = config.silence_duration
        self.sample_rate = config.sample_rate
        
    def detect_speech_end(self, audio_data: np.ndarray) -> bool:
        """Detect if speech has ended based on silence duration."""
        if len(audio_data) == 0:
            return False
        
        # Calculate energy in recent audio
        window_size = int(self.silence_duration * self.sample_rate)
        if len(audio_data) < window_size:
            return False
        
        recent_audio = audio_data[-window_size:]
        energy = np.mean(np.abs(recent_audio))
        
        return energy < self.silence_threshold
    
    def trim_silence(self, audio_data: np.ndarray) -> np.ndarray:
        """Trim silence from beginning and end of audio."""
        if len(audio_data) == 0:
            return audio_data
        
        # Find non-silent regions
        energy = np.abs(audio_data)
        non_silent = energy > self.silence_threshold
        
        if not np.any(non_silent):
            return np.array([])
        
        # Find start and end of speech
        start_idx = np.argmax(non_silent)
        end_idx = len(non_silent) - np.argmax(non_silent[::-1]) - 1
        
        return audio_data[start_idx:end_idx + 1]


class AudioPlayer:
    """Audio playback for TTS output."""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.audio = pyaudio.PyAudio()
        
    def play_audio(self, audio_data: np.ndarray, sample_rate: int) -> None:
        """Play audio data."""
        logger.info("Playing audio...")
        
        # Ensure audio is in the correct format
        if audio_data.dtype != np.float32:
            audio_data = audio_data.astype(np.float32)
        
        # Resample if necessary
        if sample_rate != self.config.sample_rate:
            audio_data = librosa.resample(
                audio_data, 
                orig_sr=sample_rate, 
                target_sr=self.config.sample_rate
            )
            sample_rate = self.config.sample_rate
        
        # Open output stream
        stream = self.audio.open(
            format=pyaudio.paFloat32,
            channels=self.config.channels,
            rate=sample_rate,
            output=True
        )
        
        try:
            # Play audio in chunks
            chunk_size = self.config.chunk_size
            for i in range(0, len(audio_data), chunk_size):
                chunk = audio_data[i:i + chunk_size]
                stream.write(chunk.tobytes())
        finally:
            stream.stop_stream()
            stream.close()
        
        logger.info("Audio playback completed")
    
    def save_audio(self, audio_data: np.ndarray, sample_rate: int, 
                   filepath: str) -> None:
        """Save audio data to file."""
        sf.write(filepath, audio_data, sample_rate)
        logger.info(f"Audio saved to: {filepath}")
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'audio'):
            self.audio.terminate()


class AudioProcessor:
    """Main audio processing class combining recording, VAD, and playback."""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.recorder = AudioRecorder(config)
        self.vad = VoiceActivityDetector(config)
        self.player = AudioPlayer(config)
        
    def record_until_silence(self, max_duration: Optional[float] = None) -> np.ndarray:
        """Record audio until silence is detected."""
        max_duration = max_duration or self.config.max_duration
        
        logger.info("Recording audio until silence...")
        
        self.recorder.start_recording()
        
        start_time = time.time()
        last_check_time = start_time
        
        try:
            while time.time() - start_time < max_duration:
                time.sleep(0.1)  # Check every 100ms
                
                # Check for silence every second
                if time.time() - last_check_time >= 1.0:
                    # Get current audio data
                    current_audio = []
                    temp_queue = queue.Queue()
                    
                    # Temporarily drain the queue
                    while not self.recorder.audio_queue.empty():
                        try:
                            chunk = self.recorder.audio_queue.get_nowait()
                            current_audio.append(chunk)
                            temp_queue.put(chunk)
                        except queue.Empty:
                            break
                    
                    # Put data back
                    while not temp_queue.empty():
                        self.recorder.audio_queue.put(temp_queue.get())
                    
                    if current_audio:
                        audio_data = np.concatenate(current_audio)
                        if self.vad.detect_speech_end(audio_data):
                            logger.info("Silence detected, stopping recording")
                            break
                    
                    last_check_time = time.time()
        
        finally:
            audio_data = self.recorder.stop_recording()
        
        # Trim silence
        audio_data = self.vad.trim_silence(audio_data)
        
        logger.info(f"Recorded {len(audio_data) / self.config.sample_rate:.2f} seconds of audio")
        
        return audio_data
    
    def load_audio_file(self, filepath: str) -> Tuple[np.ndarray, int]:
        """Load audio from file."""
        audio_data, sample_rate = librosa.load(filepath, sr=self.config.sample_rate)
        return audio_data, sample_rate
    
    def preprocess_audio(self, audio_data: np.ndarray, 
                        target_sample_rate: int = None) -> np.ndarray:
        """Preprocess audio data for model input."""
        if target_sample_rate is None:
            target_sample_rate = self.config.sample_rate
        
        # Normalize audio
        if len(audio_data) > 0:
            audio_data = audio_data / np.max(np.abs(audio_data))
        
        # Ensure minimum length (pad with zeros if too short)
        min_length = int(0.5 * target_sample_rate)  # 0.5 seconds minimum
        if len(audio_data) < min_length:
            padding = min_length - len(audio_data)
            audio_data = np.pad(audio_data, (0, padding), mode='constant')
        
        return audio_data
    
    def play_response(self, audio_data: np.ndarray, sample_rate: int) -> None:
        """Play TTS response audio."""
        self.player.play_audio(audio_data, sample_rate)
    
    def save_conversation_audio(self, audio_data: np.ndarray, sample_rate: int,
                               conversation_id: str, turn_id: int, 
                               audio_type: str = "input") -> str:
        """Save conversation audio for debugging/logging."""
        if not self.config.save_audio:
            return ""
        
        # Create filename
        timestamp = int(time.time())
        filename = f"{conversation_id}_{turn_id}_{audio_type}_{timestamp}.wav"
        filepath = Path(self.config.audio_save_dir) / filename
        
        # Ensure directory exists
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Save audio
        self.player.save_audio(audio_data, sample_rate, str(filepath))
        
        return str(filepath)
