#!/usr/bin/env python3
"""
Simple usage examples for the HMM Chatbot.

This file shows the most basic ways to use the chatbot for common tasks.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from chatbot import MultiModalChatbot, ChatbotConfig, quick_chat_text, quick_chat_voice


# Example 1: Quick text chat (no state maintained)
def example_quick_text():
    """Quick text chat without maintaining conversation state."""
    print("Example 1: Quick Text Chat")
    print("-" * 30)
    
    response = quick_chat_text("Hello, how are you?")
    print(f"Bot: {response}")


# Example 2: Quick voice chat (no state maintained)
def example_quick_voice():
    """Quick voice chat without maintaining conversation state."""
    print("\nExample 2: Quick Voice Chat")
    print("-" * 30)
    
    print("Please speak now...")
    result = quick_chat_voice(record_live=True)
    
    if result.get("error"):
        print(f"Error: {result['error']}")
    else:
        print(f"You said: {result.get('transcribed_text', '')}")
        print(f"Bot: {result.get('text_response', '')}")


# Example 3: Stateful conversation
async def example_stateful_conversation():
    """Maintain conversation state across multiple turns."""
    print("\nExample 3: Stateful Conversation")
    print("-" * 30)
    
    async with MultiModalChatbot() as chatbot:
        # First turn
        result1 = chatbot.chat_text("My name is John")
        print(f"User: My name is John")
        print(f"Bot: {result1.get('text_response', '')}")
        
        # Second turn - bot should remember the name
        result2 = chatbot.chat_text("What's my name?")
        print(f"User: What's my name?")
        print(f"Bot: {result2.get('text_response', '')}")


# Example 4: Voice with emotion detection
async def example_voice_with_emotion():
    """Voice chat with emotion detection."""
    print("\nExample 4: Voice with Emotion Detection")
    print("-" * 30)
    
    async with MultiModalChatbot() as chatbot:
        print("Please speak with some emotion...")
        
        result = chatbot.chat_voice(record_live=True)
        
        if result.get("error"):
            print(f"Error: {result['error']}")
        else:
            print(f"Transcription: {result.get('transcribed_text', '')}")
            print(f"Detected emotion: {result.get('dominant_emotion', 'neutral')}")
            print(f"Emotion scores: {result.get('detected_emotion', {})}")
            print(f"Bot response: {result.get('text_response', '')}")


# Example 5: Custom configuration
async def example_custom_config():
    """Use custom configuration."""
    print("\nExample 5: Custom Configuration")
    print("-" * 30)
    
    # Create custom config
    config = ChatbotConfig()
    config.temperature = 0.9  # More creative responses
    config.max_tokens = 100   # Shorter responses
    config.system_prompt = "You are a helpful assistant that speaks like a pirate."
    
    async with MultiModalChatbot(config) as chatbot:
        result = chatbot.chat_text("Tell me about the weather")
        print(f"Bot (pirate mode): {result.get('text_response', '')}")


# Example 6: Save and play audio responses
async def example_audio_responses():
    """Generate and save audio responses."""
    print("\nExample 6: Audio Responses")
    print("-" * 30)
    
    async with MultiModalChatbot() as chatbot:
        result = chatbot.chat_text("Tell me a short joke")
        
        print(f"Bot: {result.get('text_response', '')}")
        
        if result.get("audio_response") is not None:
            # Play the audio response
            print("Playing audio response...")
            chatbot.play_response(result)
            
            # Save the audio response
            chatbot.save_response_audio(result, "joke_response.wav")
            print("Audio saved to joke_response.wav")


# Example 7: Error handling
async def example_error_handling():
    """Demonstrate error handling."""
    print("\nExample 7: Error Handling")
    print("-" * 30)
    
    try:
        async with MultiModalChatbot() as chatbot:
            # This might fail if no microphone is available
            result = chatbot.chat_voice(record_live=True)
            
            if result.get("error"):
                print(f"Voice input failed: {result['error']}")
                print("Falling back to text input...")
                
                # Fallback to text
                result = chatbot.chat_text("Hello, I couldn't use voice input")
                print(f"Bot: {result.get('text_response', '')}")
            else:
                print(f"Voice input successful: {result.get('transcribed_text', '')}")
                print(f"Bot: {result.get('text_response', '')}")
    
    except Exception as e:
        print(f"Chatbot initialization failed: {e}")
        print("This might happen if models can't be loaded or dependencies are missing.")


async def main():
    """Run all examples."""
    print("🤖 HMM Chatbot - Simple Usage Examples")
    print("=" * 50)
    
    # Quick examples (synchronous)
    example_quick_text()
    
    # Note: Uncomment the next line if you want to test voice input
    # example_quick_voice()
    
    # Async examples
    await example_stateful_conversation()
    
    # Note: Uncomment these if you want to test voice features
    # await example_voice_with_emotion()
    
    await example_custom_config()
    await example_audio_responses()
    await example_error_handling()
    
    print("\n✅ All examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
