# HMM Chatbot Environment Configuration
# Copy this file to .env and modify the values as needed

# Model Configuration
# Override the default models with custom ones if needed
HMM_LLM_MODEL=gemini-1.5-flash
HMM_STT_MODEL=openai/whisper-large-v3
HMM_EMOTION_MODEL=speechbrain/emotion-recognition-wav2vec2-IEMOCAP
HMM_TTS_MODEL=parler-tts/parler-tts-mini-v0.1

# Google API Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here
# Alternative name for the same key
GEMINI_API_KEY=your_google_api_key_here

# Device Configuration
# Options: "cuda", "cpu", "auto" (auto-detect)
HMM_DEVICE=auto

# Cache and Storage
# Directory to store downloaded models and cache
HMM_CACHE_DIR=~/.cache/hmm-chatbot

# Audio Configuration
# Sample rate for audio processing
HMM_AUDIO_SAMPLE_RATE=16000
# Maximum recording duration in seconds
HMM_AUDIO_MAX_DURATION=30
# Silence threshold for voice activity detection
HMM_AUDIO_SILENCE_THRESHOLD=0.01
# Silence duration to stop recording (seconds)
HMM_AUDIO_SILENCE_DURATION=2.0

# LLM Generation Parameters
# Temperature for response generation (0.0 to 1.0)
HMM_TEMPERATURE=0.7
# Maximum tokens to generate
HMM_MAX_TOKENS=512
# Top-p sampling parameter
HMM_TOP_P=0.9

# Conversation Settings
# Maximum conversation history length
HMM_MAX_HISTORY_LENGTH=10
# Custom system prompt (optional)
HMM_SYSTEM_PROMPT="You are a helpful, friendly, and empathetic AI assistant."

# API Configuration (if using API mode)
HMM_API_HOST=localhost
HMM_API_PORT=8000
HMM_ENABLE_API=false

# Logging Configuration
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
HMM_LOG_LEVEL=INFO
# Log file path (optional, logs to console if not set)
HMM_LOG_FILE=

# Performance Settings
# Enable 8-bit quantization for LLM (reduces memory usage)
HMM_LLM_LOAD_IN_8BIT=true
# Enable 4-bit quantization for LLM (further reduces memory usage)
HMM_LLM_LOAD_IN_4BIT=false

# Audio Logging (for debugging)
# Save audio inputs and outputs for debugging
HMM_SAVE_AUDIO=false
# Directory to save audio files
HMM_AUDIO_SAVE_DIR=audio_logs

# Hugging Face Configuration
# Hugging Face token for accessing gated models (optional)
HUGGINGFACE_TOKEN=

# CUDA Configuration (if using CUDA)
# CUDA device index (0, 1, 2, etc.)
CUDA_VISIBLE_DEVICES=0
# Enable CUDA memory fraction (0.0 to 1.0)
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
