# 🚀 Running HMM Chatbot with UV

UV is a fast Python package manager that can significantly speed up dependency installation and project management. Here's how to use it with the HMM Chatbot project.

## 📦 Installation

### 1. Install UV
```bash
# Using pip
pip install uv

# Using curl (recommended)
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows with PowerShell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. Setup Project with UV
```bash
# Quick setup (creates venv + installs dependencies)
make setup-uv

# Or manually:
uv venv                    # Create virtual environment
uv pip install -e ".[dev]" # Install with dev dependencies
```

## 🏃‍♂️ Running Commands

### Basic Usage
```bash
# Interactive chatbot
uv run python main.py interactive

# Interactive with voice
uv run python main.py interactive --voice

# Single chat message
uv run python main.py chat "Hello, how are you?"

# Voice chat
uv run python main.py voice-chat --record

# Process audio file
uv run python main.py voice-chat --file audio.wav
```

### Using Makefile (Recommended)
```bash
# Setup project
make setup-uv

# Run interactive chatbot
make run-uv

# Run with voice enabled
make run-voice-uv

# Run examples
make demo-uv
make examples-uv

# Development
make install-dev-uv
make format
make lint
```

### Examples and Demos
```bash
# Run comprehensive demo
uv run python examples/demo.py

# Run simple usage examples
uv run python examples/simple_usage.py

# Test installation
uv run python test_installation.py
```

## 🔧 Development with UV

### Install Dependencies
```bash
# Install project in editable mode
uv pip install -e .

# Install with development dependencies
uv pip install -e ".[dev]"

# Install from requirements.txt
uv pip install -r requirements.txt
```

### Virtual Environment Management
```bash
# Create virtual environment
uv venv

# Create with specific Python version
uv venv --python 3.11

# Activate virtual environment
# On Windows:
.venv\Scripts\activate
# On Linux/Mac:
source .venv/bin/activate

# Install dependencies in venv
uv pip install -e ".[dev]"
```

### Package Management
```bash
# Add new dependency
uv add package-name

# Add development dependency
uv add --dev package-name

# Remove dependency
uv remove package-name

# Update dependencies
uv pip install --upgrade -e ".[dev]"

# Show installed packages
uv pip list

# Show dependency tree
uv pip show --files package-name
```

## ⚡ Performance Benefits

UV is significantly faster than pip:

| Operation | pip | uv | Speedup |
|-----------|-----|----|---------| 
| Install dependencies | ~60s | ~5s | 12x faster |
| Create virtual env | ~3s | ~0.1s | 30x faster |
| Resolve dependencies | ~10s | ~0.5s | 20x faster |

## 🐛 Troubleshooting

### Common Issues

1. **UV not found after installation**:
   ```bash
   # Restart terminal or source profile
   source ~/.bashrc  # Linux
   source ~/.zshrc   # macOS with zsh
   ```

2. **Virtual environment not activated**:
   ```bash
   # Make sure to activate the venv
   source .venv/bin/activate  # Linux/Mac
   .venv\Scripts\activate     # Windows
   ```

3. **Package conflicts**:
   ```bash
   # Clear UV cache
   uv cache clean
   
   # Recreate virtual environment
   rm -rf .venv
   uv venv
   uv pip install -e ".[dev]"
   ```

4. **CUDA/PyTorch issues**:
   ```bash
   # Install PyTorch with CUDA support
   uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```

## 🔄 Migration from pip

If you're currently using pip, here's how to migrate:

```bash
# 1. Install UV
pip install uv

# 2. Create requirements from current environment
pip freeze > current_requirements.txt

# 3. Create new UV environment
uv venv
source .venv/bin/activate  # Linux/Mac
# or .venv\Scripts\activate  # Windows

# 4. Install with UV
uv pip install -r current_requirements.txt

# 5. Install project
uv pip install -e ".[dev]"
```

## 📚 Additional Resources

- [UV Documentation](https://docs.astral.sh/uv/)
- [UV GitHub Repository](https://github.com/astral-sh/uv)
- [UV vs pip Performance Comparison](https://astral.sh/blog/uv)

## 🎯 Quick Start Summary

```bash
# 1. Install UV
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. Setup project
make setup-uv

# 3. Run chatbot
make run-voice-uv

# 4. Try examples
make demo-uv
```

That's it! You're now running the HMM Chatbot with UV for faster dependency management and execution.
