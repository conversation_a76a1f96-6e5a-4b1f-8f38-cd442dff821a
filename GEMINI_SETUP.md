# 🚀 Setting up HMM Chatbot with Google Gemini

This guide will help you set up the HMM Chatbot to use Google's Gemini model instead of the gated Llama models.

## 🔑 Getting Your Google API Key

1. **Visit Google AI Studio**: Go to [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)

2. **Sign in**: Use your Google account to sign in

3. **Create API Key**: 
   - Click "Create API Key"
   - Choose "Create API key in new project" or select an existing project
   - Copy the generated API key

4. **Keep it secure**: Store your API key safely - you'll need it for the chatbot

## ⚙️ Configuration

### Method 1: Environment Variables (Recommended)

1. **Create a .env file**:
   ```bash
   cp .env.example .env
   ```

2. **Edit the .env file** and add your API key:
   ```bash
   # Google API Configuration
   GOOGLE_API_KEY=your_actual_api_key_here
   
   # Model Configuration (already set to Gemini)
   HMM_LLM_MODEL=gemini-1.5-flash
   ```

### Method 2: Set Environment Variable Directly

**Windows (Command Prompt):**
```cmd
set GOOGLE_API_KEY=your_actual_api_key_here
```

**Windows (PowerShell):**
```powershell
$env:GOOGLE_API_KEY="your_actual_api_key_here"
```

**Linux/Mac:**
```bash
export GOOGLE_API_KEY="your_actual_api_key_here"
```

## 📦 Installation

1. **Install the updated dependencies**:
   ```bash
   # With UV (recommended)
   uv pip install -e .
   
   # Or with pip
   pip install -e .
   ```

2. **Install Google Generative AI specifically** (if needed):
   ```bash
   uv pip install google-generativeai
   # or
   pip install google-generativeai
   ```

## 🏃‍♂️ Running the Chatbot

### Quick Test
```bash
# Test with UV
uv run python main.py chat "Hello, how are you?"

# Test with regular Python
python main.py chat "Hello, how are you?"
```

### Interactive Mode
```bash
# With UV
uv run python main.py interactive

# With regular Python  
python main.py interactive
```

### Voice Mode
```bash
# With UV
uv run python main.py interactive --voice

# With regular Python
python main.py interactive --voice
```

## 🔧 Available Gemini Models

You can use different Gemini models by changing the `HMM_LLM_MODEL` environment variable:

- `gemini-1.5-flash` (default) - Fast and efficient
- `gemini-1.5-pro` - More capable, slower
- `gemini-1.0-pro` - Previous generation

Example:
```bash
# In .env file
HMM_LLM_MODEL=gemini-1.5-pro

# Or as environment variable
export HMM_LLM_MODEL=gemini-1.5-pro
```

## 🐛 Troubleshooting

### Common Issues

1. **"Google API key not found" error**:
   ```
   ValueError: Google API key not found. Please set GOOGLE_API_KEY or GEMINI_API_KEY environment variable.
   ```
   
   **Solution**: Make sure you've set the `GOOGLE_API_KEY` environment variable correctly.

2. **"API key invalid" error**:
   ```
   Error generating response with Gemini: 400 API key not valid
   ```
   
   **Solution**: 
   - Check that your API key is correct
   - Make sure there are no extra spaces or characters
   - Verify the API key is active in Google AI Studio

3. **"Quota exceeded" error**:
   ```
   Error generating response with Gemini: 429 Quota exceeded
   ```
   
   **Solution**: 
   - Wait a few minutes and try again
   - Check your usage limits in Google AI Studio
   - Consider upgrading your plan if needed

4. **Import error for google-generativeai**:
   ```
   ImportError: No module named 'google.generativeai'
   ```
   
   **Solution**: Install the package:
   ```bash
   uv pip install google-generativeai
   ```

### Testing Your Setup

Run this test to verify everything is working:

```bash
uv run python test_installation.py
```

This will check:
- ✅ All dependencies are installed
- ✅ Google API key is configured
- ✅ Gemini model can be loaded
- ✅ Basic functionality works

## 💡 Tips

1. **Free Tier**: Gemini has a generous free tier with rate limits
2. **Rate Limits**: If you hit rate limits, the chatbot will show an error message
3. **Model Selection**: `gemini-1.5-flash` is recommended for most use cases
4. **Fallback**: The system can fall back to open HuggingFace models if Gemini fails

## 🔄 Switching Back to HuggingFace Models

If you want to use open HuggingFace models instead of Gemini:

1. **Update your .env file**:
   ```bash
   # Use an open model that doesn't require gating
   HMM_LLM_MODEL=microsoft/DialoGPT-large
   # or
   HMM_LLM_MODEL=facebook/blenderbot-400M-distill
   ```

2. **Remove the Google API key requirement** by commenting it out:
   ```bash
   # GOOGLE_API_KEY=your_api_key_here
   ```

## 📚 Additional Resources

- [Google AI Studio](https://makersuite.google.com/)
- [Gemini API Documentation](https://ai.google.dev/docs)
- [Google Generative AI Python SDK](https://github.com/google/generative-ai-python)

## 🎉 You're Ready!

Once you've completed these steps, your HMM Chatbot will use Google's Gemini model for intelligent responses while maintaining all the voice and emotion recognition features!

Try it out:
```bash
uv run python main.py interactive --voice
```

Enjoy your new AI assistant! 🤖✨
