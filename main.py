#!/usr/bin/env python3
"""
Multi-Modal AI Chatbot with Voice and Emotion Recognition

This is the main entry point for the HMM (Human-Machine Multimodal) chatbot.
It provides both command-line interface and programmatic access to the chatbot.
"""

import asyncio
import argparse
import sys
import logging
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from chatbot import MultiModalChatbot, ChatbotConfig

console = Console()
app = typer.Typer(help="Multi-Modal AI Chatbot with Voice and Emotion Recognition")


def print_banner():
    """Print the application banner."""
    banner = Text("🤖 HMM Chatbot", style="bold blue")
    subtitle = Text("Multi-Modal AI Assistant with Voice & Emotion Recognition", style="italic")

    console.print(Panel.fit(
        f"{banner}\n{subtitle}",
        border_style="blue"
    ))


@app.command()
def interactive(
    voice: bool = typer.Option(False, "--voice", "-v", help="Enable voice input/output"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    log_level: str = typer.Option("INFO", "--log-level", "-l", help="Logging level"),
    device: Optional[str] = typer.Option(None, "--device", "-d", help="Device to use (cuda/cpu/auto)")
):
    """Start interactive chatbot session."""
    print_banner()

    # Create configuration
    config = ChatbotConfig.from_env()
    config.log_level = log_level

    if device:
        config.llm_config.device = device
        config.stt_config.device = device
        config.emotion_config.device = device
        config.tts_config.device = device

    # Run interactive session
    asyncio.run(run_interactive_session(config, voice))


@app.command()
def chat(
    message: str = typer.Argument(..., help="Message to send to the chatbot"),
    voice_output: bool = typer.Option(False, "--voice-output", "-vo", help="Generate voice output"),
    save_audio: Optional[str] = typer.Option(None, "--save-audio", "-s", help="Save audio output to file"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    device: Optional[str] = typer.Option(None, "--device", "-d", help="Device to use (cuda/cpu/auto)")
):
    """Send a single message to the chatbot."""
    # Create configuration
    config = ChatbotConfig.from_env()

    if device:
        config.llm_config.device = device
        config.stt_config.device = device
        config.emotion_config.device = device
        config.tts_config.device = device

    # Run single chat
    asyncio.run(run_single_chat(message, config, voice_output, save_audio))


@app.command()
def voice_chat(
    audio_file: Optional[str] = typer.Option(None, "--file", "-f", help="Audio file to process"),
    record_live: bool = typer.Option(True, "--record", "-r", help="Record live audio"),
    save_response: Optional[str] = typer.Option(None, "--save", "-s", help="Save response audio to file"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    device: Optional[str] = typer.Option(None, "--device", "-d", help="Device to use (cuda/cpu/auto)")
):
    """Process voice input and generate voice response."""
    # Create configuration
    config = ChatbotConfig.from_env()

    if device:
        config.llm_config.device = device
        config.stt_config.device = device
        config.emotion_config.device = device
        config.tts_config.device = device

    # Run voice chat
    asyncio.run(run_voice_chat(config, audio_file, record_live, save_response))


async def run_interactive_session(config: ChatbotConfig, voice_enabled: bool):
    """Run an interactive chatbot session."""
    console.print("🚀 Initializing chatbot...", style="yellow")

    try:
        async with MultiModalChatbot(config) as chatbot:
            console.print("✅ Chatbot ready!", style="green")
            console.print("\nType 'quit', 'exit', or 'bye' to end the conversation.")
            console.print("Type 'voice' to switch to voice input mode.")
            console.print("Type 'clear' to clear conversation history.")
            console.print("Type 'stats' to see conversation statistics.\n")

            while True:
                try:
                    if voice_enabled:
                        console.print("🎤 [bold blue]Speak now (or type 'text' for text mode):[/bold blue]")

                        # Check if user wants to switch to text
                        user_input = console.input("Press Enter to record, or type message: ").strip()

                        if user_input.lower() == "text":
                            voice_enabled = False
                            continue
                        elif user_input:
                            # Text input provided
                            result = chatbot.chat_text(user_input)
                        else:
                            # Voice input
                            result = chatbot.chat_voice(record_live=True)
                    else:
                        user_input = Prompt.ask("💬 [bold blue]You[/bold blue]").strip()

                        if user_input.lower() in ["quit", "exit", "bye"]:
                            break
                        elif user_input.lower() == "voice":
                            voice_enabled = True
                            continue
                        elif user_input.lower() == "clear":
                            chatbot.clear_conversation()
                            console.print("🗑️ Conversation history cleared.", style="yellow")
                            continue
                        elif user_input.lower() == "stats":
                            stats = chatbot.get_stats()
                            console.print(f"📊 Stats: {stats}", style="cyan")
                            continue

                        result = chatbot.chat_text(user_input)

                    # Display results
                    if result.get("error"):
                        console.print(f"❌ Error: {result['error']}", style="red")
                    else:
                        # Show transcription if voice input was used
                        if result.get("transcribed_text"):
                            console.print(f"🎤 Heard: {result['transcribed_text']}", style="dim")

                        # Show emotion if detected
                        if result.get("dominant_emotion") != "neutral":
                            emotion = result["dominant_emotion"]
                            console.print(f"😊 Detected emotion: {emotion}", style="magenta")

                        # Show response
                        response = result.get("text_response", "")
                        console.print(f"🤖 [bold green]Bot:[/bold green] {response}")

                        # Play audio response if available
                        if result.get("audio_response") is not None:
                            console.print("🔊 Playing audio response...", style="cyan")
                            chatbot.play_response(result)

                except KeyboardInterrupt:
                    console.print("\n👋 Goodbye!", style="yellow")
                    break
                except Exception as e:
                    console.print(f"❌ Error: {e}", style="red")

    except Exception as e:
        console.print(f"❌ Failed to initialize chatbot: {e}", style="red")
        sys.exit(1)


async def run_single_chat(message: str, config: ChatbotConfig,
                         voice_output: bool, save_audio: Optional[str]):
    """Run a single chat interaction."""
    console.print("🚀 Initializing chatbot...", style="yellow")

    try:
        async with MultiModalChatbot(config) as chatbot:
            result = chatbot.chat_text(message)

            if result.get("error"):
                console.print(f"❌ Error: {result['error']}", style="red")
                return

            response = result.get("text_response", "")
            console.print(f"🤖 Response: {response}")

            if voice_output and result.get("audio_response") is not None:
                console.print("🔊 Playing audio response...", style="cyan")
                chatbot.play_response(result)

            if save_audio and result.get("audio_response") is not None:
                chatbot.save_response_audio(result, save_audio)
                console.print(f"💾 Audio saved to: {save_audio}", style="green")

    except Exception as e:
        console.print(f"❌ Error: {e}", style="red")
        sys.exit(1)


async def run_voice_chat(config: ChatbotConfig, audio_file: Optional[str],
                        record_live: bool, save_response: Optional[str]):
    """Run voice chat interaction."""
    console.print("🚀 Initializing chatbot...", style="yellow")

    try:
        async with MultiModalChatbot(config) as chatbot:
            if audio_file:
                console.print(f"🎵 Processing audio file: {audio_file}", style="cyan")
                result = chatbot.chat_voice(audio_file=audio_file, record_live=False)
            else:
                console.print("🎤 Recording audio... (speak now)", style="cyan")
                result = chatbot.chat_voice(record_live=record_live)

            if result.get("error"):
                console.print(f"❌ Error: {result['error']}", style="red")
                return

            # Show transcription
            transcription = result.get("transcribed_text", "")
            console.print(f"🎤 Transcription: {transcription}")

            # Show emotion
            emotion = result.get("dominant_emotion", "neutral")
            if emotion != "neutral":
                console.print(f"😊 Detected emotion: {emotion}", style="magenta")

            # Show response
            response = result.get("text_response", "")
            console.print(f"🤖 Response: {response}")

            # Play audio response
            if result.get("audio_response") is not None:
                console.print("🔊 Playing audio response...", style="cyan")
                chatbot.play_response(result)

                if save_response:
                    chatbot.save_response_audio(result, save_response)
                    console.print(f"💾 Audio saved to: {save_response}", style="green")

    except Exception as e:
        console.print(f"❌ Error: {e}", style="red")
        sys.exit(1)


def main():
    """Main entry point."""
    app()


if __name__ == "__main__":
    main()
