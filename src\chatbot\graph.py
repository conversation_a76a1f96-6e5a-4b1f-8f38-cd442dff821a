"""LangGraph workflow definition for the multi-modal chatbot."""

import logging
from typing import Dict, Any, List, Optional, TypedDict
from dataclasses import dataclass
import numpy as np

from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from .models import ModelManager
from .audio_handler import AudioProcessor
from .config import ChatbotConfig

logger = logging.getLogger(__name__)


class ConversationState(TypedDict):
    """State for the conversation graph."""
    messages: List[BaseMessage]
    audio_input: Optional[np.ndarray]
    audio_sample_rate: Optional[int]
    transcribed_text: Optional[str]
    detected_emotion: Optional[Dict[str, float]]
    dominant_emotion: Optional[str]
    llm_response: Optional[str]
    tts_audio: Optional[np.ndarray]
    tts_sample_rate: Optional[int]
    conversation_id: str
    turn_id: int
    error: Optional[str]


@dataclass
class ConversationTurn:
    """Represents a single conversation turn."""
    turn_id: int
    user_input: str
    user_emotion: Dict[str, float]
    dominant_emotion: str
    bot_response: str
    audio_input: Optional[np.ndarray] = None
    audio_output: Optional[np.ndarray] = None


class ChatbotGraph:
    """LangGraph-based chatbot workflow."""
    
    def __init__(self, config: ChatbotConfig, model_manager: ModelManager, 
                 audio_processor: AudioProcessor):
        self.config = config
        self.model_manager = model_manager
        self.audio_processor = audio_processor
        self.conversation_history: List[ConversationTurn] = []
        
        # Build the graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow."""
        # Create the graph
        workflow = StateGraph(ConversationState)
        
        # Add nodes
        workflow.add_node("process_audio_input", self._process_audio_input)
        workflow.add_node("transcribe_speech", self._transcribe_speech)
        workflow.add_node("detect_emotion", self._detect_emotion)
        workflow.add_node("generate_response", self._generate_response)
        workflow.add_node("synthesize_speech", self._synthesize_speech)
        workflow.add_node("handle_error", self._handle_error)
        
        # Set entry point
        workflow.set_entry_point("process_audio_input")
        
        # Add edges
        workflow.add_conditional_edges(
            "process_audio_input",
            self._should_transcribe,
            {
                "transcribe": "transcribe_speech",
                "text_only": "detect_emotion",
                "error": "handle_error"
            }
        )
        
        workflow.add_edge("transcribe_speech", "detect_emotion")
        workflow.add_edge("detect_emotion", "generate_response")
        workflow.add_edge("generate_response", "synthesize_speech")
        workflow.add_edge("synthesize_speech", END)
        workflow.add_edge("handle_error", END)
        
        return workflow.compile()
    
    def _process_audio_input(self, state: ConversationState) -> ConversationState:
        """Process audio input if present."""
        logger.info("Processing audio input...")
        
        try:
            if state.get("audio_input") is not None:
                # Preprocess audio for models
                audio_data = state["audio_input"]
                sample_rate = state.get("audio_sample_rate", self.config.audio_config.sample_rate)
                
                # Preprocess audio
                processed_audio = self.audio_processor.preprocess_audio(audio_data)
                
                state["audio_input"] = processed_audio
                state["audio_sample_rate"] = sample_rate
                
                logger.info(f"Audio input processed: {len(processed_audio)} samples")
            
            return state
            
        except Exception as e:
            logger.error(f"Error processing audio input: {e}")
            state["error"] = f"Audio processing error: {str(e)}"
            return state
    
    def _transcribe_speech(self, state: ConversationState) -> ConversationState:
        """Transcribe speech to text using STT model."""
        logger.info("Transcribing speech...")
        
        try:
            audio_data = state["audio_input"]
            sample_rate = state["audio_sample_rate"]
            
            # Transcribe using STT model
            transcription = self.model_manager.stt.transcribe(audio_data, sample_rate)
            
            state["transcribed_text"] = transcription
            logger.info(f"Transcription: {transcription}")
            
            return state
            
        except Exception as e:
            logger.error(f"Error transcribing speech: {e}")
            state["error"] = f"Transcription error: {str(e)}"
            return state
    
    def _detect_emotion(self, state: ConversationState) -> ConversationState:
        """Detect emotion from audio or infer from text."""
        logger.info("Detecting emotion...")
        
        try:
            if state.get("audio_input") is not None:
                # Detect emotion from audio
                audio_data = state["audio_input"]
                emotion_probs = self.model_manager.emotion.predict_emotion(audio_data)
                
                # Find dominant emotion
                dominant_emotion = max(emotion_probs, key=emotion_probs.get)
                
                state["detected_emotion"] = emotion_probs
                state["dominant_emotion"] = dominant_emotion
                
                logger.info(f"Detected emotions: {emotion_probs}")
                logger.info(f"Dominant emotion: {dominant_emotion}")
            else:
                # Default to neutral if no audio
                state["detected_emotion"] = {"neutral": 1.0}
                state["dominant_emotion"] = "neutral"
            
            return state
            
        except Exception as e:
            logger.error(f"Error detecting emotion: {e}")
            # Default to neutral on error
            state["detected_emotion"] = {"neutral": 1.0}
            state["dominant_emotion"] = "neutral"
            return state
    
    def _generate_response(self, state: ConversationState) -> ConversationState:
        """Generate response using LLM."""
        logger.info("Generating response...")
        
        try:
            # Get user input (transcribed or direct text)
            user_input = state.get("transcribed_text") or ""
            if not user_input and state.get("messages"):
                # Extract from last message if available
                last_message = state["messages"][-1]
                if isinstance(last_message, HumanMessage):
                    user_input = last_message.content
            
            if not user_input:
                state["error"] = "No user input found"
                return state
            
            # Get emotion context
            dominant_emotion = state.get("dominant_emotion", "neutral")
            
            # Build conversation context
            context = self._build_conversation_context(user_input, dominant_emotion)
            
            # Generate response using LLM
            response = self.model_manager.llm.generate(
                prompt=context,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                top_p=self.config.top_p,
                do_sample=self.config.do_sample
            )
            
            state["llm_response"] = response
            
            # Add to messages
            if "messages" not in state:
                state["messages"] = []
            
            state["messages"].append(HumanMessage(content=user_input))
            state["messages"].append(AIMessage(content=response))
            
            logger.info(f"Generated response: {response}")
            
            return state
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            state["error"] = f"Response generation error: {str(e)}"
            return state
    
    def _synthesize_speech(self, state: ConversationState) -> ConversationState:
        """Synthesize speech from text response."""
        logger.info("Synthesizing speech...")
        
        try:
            response_text = state["llm_response"]
            dominant_emotion = state.get("dominant_emotion", "neutral")
            
            # Synthesize speech
            audio_data, sample_rate = self.model_manager.tts.synthesize(
                response_text, 
                emotion=dominant_emotion
            )
            
            state["tts_audio"] = audio_data
            state["tts_sample_rate"] = sample_rate
            
            logger.info(f"Speech synthesized: {len(audio_data)} samples at {sample_rate}Hz")
            
            return state
            
        except Exception as e:
            logger.error(f"Error synthesizing speech: {e}")
            state["error"] = f"Speech synthesis error: {str(e)}"
            return state
    
    def _handle_error(self, state: ConversationState) -> ConversationState:
        """Handle errors in the workflow."""
        error_msg = state.get("error", "Unknown error occurred")
        logger.error(f"Handling error: {error_msg}")
        
        # Generate fallback response
        fallback_response = "I'm sorry, I encountered an error processing your request. Could you please try again?"
        
        state["llm_response"] = fallback_response
        
        # Try to synthesize fallback speech
        try:
            audio_data, sample_rate = self.model_manager.tts.synthesize(fallback_response)
            state["tts_audio"] = audio_data
            state["tts_sample_rate"] = sample_rate
        except Exception as e:
            logger.error(f"Error synthesizing fallback speech: {e}")
        
        return state
    
    def _should_transcribe(self, state: ConversationState) -> str:
        """Determine if we should transcribe audio or process text directly."""
        if state.get("error"):
            return "error"
        elif state.get("audio_input") is not None:
            return "transcribe"
        else:
            return "text_only"
    
    def _build_conversation_context(self, user_input: str, emotion: str) -> str:
        """Build conversation context for the LLM."""
        # Start with system prompt
        context = self.config.system_prompt + "\n\n"
        
        # Add emotion context
        emotion_context = {
            "happy": "The user seems happy and upbeat. Respond with enthusiasm and positivity.",
            "sad": "The user seems sad or melancholic. Respond with empathy and gentle support.",
            "angry": "The user seems frustrated or angry. Respond calmly and try to be helpful.",
            "neutral": "The user has a neutral tone. Respond naturally and helpfully."
        }
        
        context += emotion_context.get(emotion, emotion_context["neutral"]) + "\n\n"
        
        # Add recent conversation history
        if self.conversation_history:
            context += "Recent conversation:\n"
            for turn in self.conversation_history[-self.config.max_history_length:]:
                context += f"User: {turn.user_input}\n"
                context += f"Assistant: {turn.bot_response}\n"
            context += "\n"
        
        # Add current user input
        context += f"User: {user_input}\n"
        context += "Assistant: "
        
        return context
    
    def process_turn(self, user_input: str = None, audio_input: np.ndarray = None,
                    audio_sample_rate: int = None, conversation_id: str = "default") -> Dict[str, Any]:
        """Process a single conversation turn."""
        turn_id = len(self.conversation_history) + 1
        
        # Create initial state
        initial_state = ConversationState(
            messages=[],
            audio_input=audio_input,
            audio_sample_rate=audio_sample_rate,
            transcribed_text=user_input,
            detected_emotion=None,
            dominant_emotion=None,
            llm_response=None,
            tts_audio=None,
            tts_sample_rate=None,
            conversation_id=conversation_id,
            turn_id=turn_id,
            error=None
        )
        
        # Run the graph
        final_state = self.graph.invoke(initial_state)
        
        # Create conversation turn record
        turn = ConversationTurn(
            turn_id=turn_id,
            user_input=final_state.get("transcribed_text", user_input or ""),
            user_emotion=final_state.get("detected_emotion", {}),
            dominant_emotion=final_state.get("dominant_emotion", "neutral"),
            bot_response=final_state.get("llm_response", ""),
            audio_input=audio_input,
            audio_output=final_state.get("tts_audio")
        )
        
        # Add to history
        self.conversation_history.append(turn)
        
        # Return results
        return {
            "text_response": final_state.get("llm_response", ""),
            "audio_response": final_state.get("tts_audio"),
            "audio_sample_rate": final_state.get("tts_sample_rate"),
            "detected_emotion": final_state.get("detected_emotion", {}),
            "dominant_emotion": final_state.get("dominant_emotion", "neutral"),
            "transcribed_text": final_state.get("transcribed_text"),
            "error": final_state.get("error"),
            "turn_id": turn_id
        }
    
    def clear_history(self):
        """Clear conversation history."""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
