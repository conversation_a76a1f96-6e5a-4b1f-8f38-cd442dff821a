#!/usr/bin/env python3
"""
Setup script for configuring Google Gemini API with HMM Chatbot.

This script helps users set up their Google API key and test the Gemini integration.
"""

import os
import sys
from pathlib import Path
import webbrowser
from typing import Optional

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.prompt import Prompt, Confirm
    from rich.text import Text
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    
    # Fallback console class
    class Console:
        def print(self, *args, **kwargs):
            print(*args)
    
    class Prompt:
        @staticmethod
        def ask(prompt, **kwargs):
            return input(f"{prompt}: ")
    
    class Confirm:
        @staticmethod
        def ask(prompt, **kwargs):
            response = input(f"{prompt} (y/n): ").lower()
            return response in ['y', 'yes', 'true', '1']

console = Console()


def print_banner():
    """Print the setup banner."""
    if RICH_AVAILABLE:
        banner = Text("🔑 Gemini API Setup", style="bold blue")
        subtitle = Text("Configure Google Gemini for HMM Chatbot", style="italic")
        
        console.print(Panel.fit(
            f"{banner}\n{subtitle}",
            border_style="blue"
        ))
    else:
        console.print("🔑 Gemini API Setup")
        console.print("Configure Google Gemini for HMM Chatbot")
        console.print("=" * 50)


def check_existing_api_key() -> Optional[str]:
    """Check if API key is already configured."""
    # Check environment variables
    api_key = os.getenv('GOOGLE_API_KEY') or os.getenv('GEMINI_API_KEY')
    if api_key:
        return api_key
    
    # Check .env file
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('GOOGLE_API_KEY=') and not line.endswith('your_google_api_key_here'):
                    return line.split('=', 1)[1]
                elif line.startswith('GEMINI_API_KEY=') and not line.endswith('your_google_api_key_here'):
                    return line.split('=', 1)[1]
    
    return None


def open_api_key_page():
    """Open the Google AI Studio API key page."""
    url = "https://makersuite.google.com/app/apikey"
    console.print(f"🌐 Opening Google AI Studio in your browser...")
    console.print(f"URL: {url}")
    
    try:
        webbrowser.open(url)
        console.print("✅ Browser opened successfully!")
    except Exception as e:
        console.print(f"❌ Could not open browser: {e}")
        console.print(f"Please manually visit: {url}")


def get_api_key_from_user() -> str:
    """Get API key from user input."""
    console.print("\n📋 Instructions:")
    console.print("1. Sign in to Google AI Studio")
    console.print("2. Click 'Create API Key'")
    console.print("3. Choose 'Create API key in new project' or select existing project")
    console.print("4. Copy the generated API key")
    console.print("5. Paste it below")
    
    while True:
        api_key = Prompt.ask("\n🔑 Enter your Google API key").strip()
        
        if not api_key:
            console.print("❌ API key cannot be empty. Please try again.")
            continue
        
        if api_key == "your_google_api_key_here":
            console.print("❌ Please enter your actual API key, not the placeholder.")
            continue
        
        if len(api_key) < 20:
            console.print("❌ API key seems too short. Please check and try again.")
            continue
        
        return api_key


def save_api_key_to_env(api_key: str):
    """Save API key to .env file."""
    env_file = Path('.env')
    
    # Read existing .env file or create from template
    if env_file.exists():
        with open(env_file, 'r') as f:
            lines = f.readlines()
    else:
        # Copy from .env.example
        example_file = Path('.env.example')
        if example_file.exists():
            with open(example_file, 'r') as f:
                lines = f.readlines()
        else:
            lines = [
                "# HMM Chatbot Environment Configuration\n",
                "\n",
                "# Google API Configuration\n",
                "GOOGLE_API_KEY=your_google_api_key_here\n",
                "\n",
                "# Model Configuration\n",
                "HMM_LLM_MODEL=gemini-1.5-flash\n"
            ]
    
    # Update the API key line
    updated_lines = []
    api_key_updated = False
    
    for line in lines:
        if line.startswith('GOOGLE_API_KEY='):
            updated_lines.append(f'GOOGLE_API_KEY={api_key}\n')
            api_key_updated = True
        elif line.startswith('GEMINI_API_KEY='):
            updated_lines.append(f'GEMINI_API_KEY={api_key}\n')
        else:
            updated_lines.append(line)
    
    # Add API key if not found
    if not api_key_updated:
        updated_lines.append(f'\n# Google API Configuration\nGOOGLE_API_KEY={api_key}\n')
    
    # Write back to .env file
    with open(env_file, 'w') as f:
        f.writelines(updated_lines)
    
    console.print(f"✅ API key saved to {env_file}")


def test_api_key(api_key: str) -> bool:
    """Test if the API key works."""
    console.print("\n🧪 Testing API key...")
    
    try:
        # Set environment variable temporarily
        os.environ['GOOGLE_API_KEY'] = api_key
        
        # Try to import and test Gemini
        import google.generativeai as genai
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Test with a simple prompt
        response = model.generate_content("Say 'Hello, I am working!' in a friendly way.")
        
        if response and response.text:
            console.print("✅ API key is working!")
            console.print(f"🤖 Test response: {response.text.strip()}")
            return True
        else:
            console.print("❌ API key test failed: No response received")
            return False
            
    except ImportError:
        console.print("❌ google-generativeai package not installed")
        console.print("Please run: uv pip install google-generativeai")
        return False
    except Exception as e:
        console.print(f"❌ API key test failed: {e}")
        return False


def main():
    """Main setup function."""
    print_banner()
    
    # Check if API key is already configured
    existing_key = check_existing_api_key()
    if existing_key:
        console.print(f"✅ Found existing API key: {existing_key[:10]}...")
        
        if Confirm.ask("Do you want to test the existing API key?", default=True):
            if test_api_key(existing_key):
                console.print("\n🎉 Your Gemini API is already configured and working!")
                console.print("You can now run the chatbot with:")
                console.print("  uv run python main.py interactive")
                return
            else:
                console.print("❌ Existing API key is not working.")
        
        if not Confirm.ask("Do you want to configure a new API key?", default=True):
            return
    
    # Guide user to get API key
    console.print("\n🚀 Let's set up your Google Gemini API key!")
    
    if Confirm.ask("Do you want to open Google AI Studio to get your API key?", default=True):
        open_api_key_page()
        console.print("\nAfter getting your API key, come back here to continue...")
        input("Press Enter when you have your API key ready...")
    
    # Get API key from user
    api_key = get_api_key_from_user()
    
    # Test the API key
    if test_api_key(api_key):
        # Save to .env file
        save_api_key_to_env(api_key)
        
        console.print("\n🎉 Setup completed successfully!")
        console.print("\nNext steps:")
        console.print("1. Run: uv run python test_installation.py")
        console.print("2. Run: uv run python main.py interactive")
        console.print("3. Try: uv run python main.py chat 'Hello!'")
        
    else:
        console.print("\n❌ Setup failed. Please check your API key and try again.")
        console.print("You can run this script again with: python setup_gemini.py")


if __name__ == "__main__":
    main()
