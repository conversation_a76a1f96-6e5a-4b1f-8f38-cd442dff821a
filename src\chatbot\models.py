"""Model wrappers for LLM, STT, TTS, and emotion recognition."""

import torch
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import numpy as np
import soundfile as sf
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoProcessor,
    WhisperProcessor, WhisperForConditionalGeneration,
    pipeline
)
import speechbrain as sb
from speechbrain.inference import EncoderClassifier
import google.generativeai as genai
import os

from .config import ChatbotConfig, ModelConfig

logger = logging.getLogger(__name__)


class GeminiLLMModel:
    """Wrapper for Google Gemini LLM."""

    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.api_key = None

    def load(self):
        """Load the Gemini model."""
        logger.info(f"Loading Gemini model: {self.config.model_name}")

        # Get API key from environment
        self.api_key = os.getenv('GOOGLE_API_KEY') or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError(
                "Google API key not found. Please set GOOGLE_API_KEY or GEMINI_API_KEY environment variable. "
                "Get your API key from: https://makersuite.google.com/app/apikey"
            )

        # Configure Gemini
        genai.configure(api_key=self.api_key)

        # Initialize model
        model_name = self.config.model_name or "gemini-1.5-flash"
        self.model = genai.GenerativeModel(model_name)

        logger.info(f"Gemini model loaded: {model_name}")

    def generate(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7,
                 top_p: float = 0.9, do_sample: bool = True) -> str:
        """Generate text response from Gemini."""
        if self.model is None:
            self.load()

        try:
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
            )

            # Generate response
            response = self.model.generate_content(
                prompt,
                generation_config=generation_config
            )

            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating response with Gemini: {e}")
            return "I apologize, but I encountered an error generating a response. Please try again."


class HuggingFaceLLMModel:
    """Wrapper for HuggingFace LLM models (fallback for open models)."""

    def __init__(self, config: ModelConfig):
        self.config = config
        self.tokenizer = None
        self.model = None
        self.device = None

    def load(self):
        """Load the LLM model and tokenizer."""
        logger.info(f"Loading HuggingFace LLM model: {self.config.model_name}")

        # Determine device
        if self.config.device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = self.config.device

        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                cache_dir=self.config.cache_dir,
                trust_remote_code=self.config.trust_remote_code
            )

            # Set pad token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Load model
            model_kwargs = {
                "cache_dir": self.config.cache_dir,
                "trust_remote_code": self.config.trust_remote_code,
                "torch_dtype": torch.float16 if self.config.torch_dtype == "auto" else self.config.torch_dtype
            }

            if self.config.load_in_8bit:
                model_kwargs["load_in_8bit"] = True
            elif self.config.load_in_4bit:
                model_kwargs["load_in_4bit"] = True

            self.model = AutoModelForCausalLM.from_pretrained(
                self.config.model_name,
                **model_kwargs
            )

            if not (self.config.load_in_8bit or self.config.load_in_4bit):
                self.model = self.model.to(self.device)

            logger.info(f"HuggingFace LLM model loaded on device: {self.device}")

        except Exception as e:
            logger.error(f"Error loading HuggingFace model: {e}")
            raise

    def generate(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7,
                 top_p: float = 0.9, do_sample: bool = True) -> str:
        """Generate text response from the LLM."""
        if self.model is None:
            self.load()

        try:
            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=do_sample,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Remove the input prompt from the response
            if response.startswith(prompt):
                response = response[len(prompt):].strip()

            return response

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I encountered an error generating a response. Please try again."


# Factory function to create the appropriate LLM model
def create_llm_model(config: ModelConfig):
    """Create the appropriate LLM model based on configuration."""
    model_name = config.model_name.lower()

    if "gemini" in model_name or model_name.startswith("google/"):
        return GeminiLLMModel(config)
    else:
        return HuggingFaceLLMModel(config)


class STTModel:
    """Wrapper for Speech-to-Text (Whisper)."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.processor = None
        self.model = None
        self.device = None
    
    def load(self):
        """Load the STT model."""
        logger.info(f"Loading STT model: {self.config.model_name}")
        
        # Determine device
        if self.config.device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = self.config.device
        
        # Load processor and model
        self.processor = WhisperProcessor.from_pretrained(
            self.config.model_name,
            cache_dir=self.config.cache_dir
        )
        
        self.model = WhisperForConditionalGeneration.from_pretrained(
            self.config.model_name,
            cache_dir=self.config.cache_dir,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
        ).to(self.device)
        
        logger.info(f"STT model loaded on device: {self.device}")
    
    def transcribe(self, audio_data: np.ndarray, sample_rate: int = 16000) -> str:
        """Transcribe audio to text."""
        if self.model is None:
            self.load()
        
        # Process audio
        inputs = self.processor(
            audio_data,
            sampling_rate=sample_rate,
            return_tensors="pt"
        )
        
        # Move to device
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Generate transcription
        with torch.no_grad():
            predicted_ids = self.model.generate(**inputs)
        
        # Decode transcription
        transcription = self.processor.batch_decode(predicted_ids, skip_special_tokens=True)[0]
        
        return transcription.strip()


class EmotionModel:
    """Wrapper for emotion recognition."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.classifier = None
    
    def load(self):
        """Load the emotion recognition model."""
        logger.info(f"Loading emotion model: {self.config.model_name}")
        
        # Load SpeechBrain emotion classifier
        self.classifier = EncoderClassifier.from_hparams(
            source=self.config.model_name,
            savedir=self.config.cache_dir
        )
        
        logger.info("Emotion model loaded")
    
    def predict_emotion(self, audio_data: np.ndarray, sample_rate: int = 16000) -> Dict[str, float]:
        """Predict emotion from audio."""
        if self.classifier is None:
            self.load()
        
        # Convert to tensor
        audio_tensor = torch.tensor(audio_data).unsqueeze(0)
        
        # Predict emotion
        out_prob, score, index, text_lab = self.classifier.classify_batch(audio_tensor)
        
        # Get emotion probabilities
        emotions = ["angry", "happy", "sad", "neutral"]  # IEMOCAP emotions
        emotion_probs = {}
        
        for i, emotion in enumerate(emotions):
            if i < len(out_prob[0]):
                emotion_probs[emotion] = float(out_prob[0][i])
            else:
                emotion_probs[emotion] = 0.0
        
        return emotion_probs


class TTSModel:
    """Wrapper for Text-to-Speech (Parler TTS)."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.pipeline = None
        self.device = None
    
    def load(self):
        """Load the TTS model."""
        logger.info(f"Loading TTS model: {self.config.model_name}")
        
        # Determine device
        if self.config.device == "auto":
            self.device = 0 if torch.cuda.is_available() else -1
        else:
            self.device = 0 if self.config.device == "cuda" else -1
        
        # Load TTS pipeline
        self.pipeline = pipeline(
            "text-to-speech",
            model=self.config.model_name,
            device=self.device
        )
        
        logger.info(f"TTS model loaded on device: {self.device}")
    
    def synthesize(self, text: str, emotion: str = "neutral") -> np.ndarray:
        """Synthesize speech from text."""
        if self.pipeline is None:
            self.load()
        
        # Adjust text based on emotion
        emotion_prompts = {
            "happy": "A cheerful and upbeat voice says: ",
            "sad": "A gentle and melancholic voice says: ",
            "angry": "A firm and assertive voice says: ",
            "neutral": "A calm and friendly voice says: "
        }
        
        prompt = emotion_prompts.get(emotion, emotion_prompts["neutral"])
        full_text = prompt + text
        
        # Generate speech
        result = self.pipeline(full_text)
        
        # Extract audio data
        audio_data = result["audio"]
        sample_rate = result["sampling_rate"]
        
        return audio_data, sample_rate


class ModelManager:
    """Manager for all models."""

    def __init__(self, config: ChatbotConfig):
        self.config = config
        self.llm = create_llm_model(config.llm_config)
        self.stt = STTModel(config.stt_config)
        self.emotion = EmotionModel(config.emotion_config)
        self.tts = TTSModel(config.tts_config)

        self._loaded = False
    
    def load_all(self):
        """Load all models."""
        if self._loaded:
            return
        
        logger.info("Loading all models...")
        
        try:
            self.llm.load()
            self.stt.load()
            self.emotion.load()
            self.tts.load()
            self._loaded = True
            logger.info("All models loaded successfully")
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            raise
    
    def unload_all(self):
        """Unload all models to free memory."""
        logger.info("Unloading all models...")
        
        # Clear model references
        self.llm.model = None
        self.llm.tokenizer = None
        self.stt.model = None
        self.stt.processor = None
        self.emotion.classifier = None
        self.tts.pipeline = None
        
        # Clear CUDA cache if available
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self._loaded = False
        logger.info("All models unloaded")
    
    @property
    def is_loaded(self) -> bool:
        """Check if models are loaded."""
        return self._loaded
