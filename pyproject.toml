[project]
name = "hmm"
version = "0.1.0"
description = "Multi-modal AI Chatbot with Voice and Emotion Recognition"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "langgraph>=0.0.40",
    "langchain>=0.1.0",
    "transformers>=4.35.0",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "speechbrain>=0.5.15",
    "datasets>=2.14.0",
    "soundfile>=0.12.1",
    "librosa>=0.10.1",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "pyaudio>=0.2.11",
    "pydub>=0.25.1",
    "accelerate>=0.24.0",
    "huggingface-hub>=0.17.0",
    "tokenizers>=0.14.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.0",
    "asyncio-mqtt>=0.13.0",
    "pydantic>=2.4.0",
    "python-dotenv>=1.0.0",
    "rich>=13.6.0",
    "typer>=0.9.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["hmm"]

[project.scripts]
hmm-chatbot = "hmm.main:main"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0"
]
