#!/usr/bin/env python3
"""
Demo script showing how to use the HMM Chatbot programmatically.

This script demonstrates various ways to interact with the chatbot:
1. Text-only conversation
2. Voice input with text output
3. Full voice conversation (voice input + voice output)
4. Emotion-aware responses
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from chatbot import MultiModalChatbot, ChatbotConfig


async def demo_text_chat():
    """Demonstrate text-only chat."""
    print("🔤 Demo: Text Chat")
    print("=" * 50)
    
    config = ChatbotConfig()
    
    async with MultiModalChatbot(config) as chatbot:
        messages = [
            "Hello! How are you today?",
            "What's the weather like?",
            "Tell me a joke",
            "What can you help me with?"
        ]
        
        for message in messages:
            print(f"👤 User: {message}")
            
            result = chatbot.chat_text(message)
            
            if result.get("error"):
                print(f"❌ Error: {result['error']}")
            else:
                response = result.get("text_response", "")
                print(f"🤖 Bot: {response}")
            
            print()


async def demo_voice_to_text():
    """Demonstrate voice input with text output."""
    print("🎤 Demo: Voice to Text")
    print("=" * 50)
    
    config = ChatbotConfig()
    
    async with MultiModalChatbot(config) as chatbot:
        print("Please speak when prompted...")
        
        # Record voice input
        result = chatbot.chat_voice(record_live=True)
        
        if result.get("error"):
            print(f"❌ Error: {result['error']}")
        else:
            transcription = result.get("transcribed_text", "")
            emotion = result.get("dominant_emotion", "neutral")
            response = result.get("text_response", "")
            
            print(f"🎤 Transcription: {transcription}")
            print(f"😊 Detected emotion: {emotion}")
            print(f"🤖 Response: {response}")


async def demo_full_voice_chat():
    """Demonstrate full voice conversation."""
    print("🗣️ Demo: Full Voice Chat")
    print("=" * 50)
    
    config = ChatbotConfig()
    
    async with MultiModalChatbot(config) as chatbot:
        print("Please speak when prompted...")
        
        # Record voice input
        result = chatbot.chat_voice(record_live=True)
        
        if result.get("error"):
            print(f"❌ Error: {result['error']}")
        else:
            transcription = result.get("transcribed_text", "")
            emotion = result.get("dominant_emotion", "neutral")
            response = result.get("text_response", "")
            
            print(f"🎤 Transcription: {transcription}")
            print(f"😊 Detected emotion: {emotion}")
            print(f"🤖 Response: {response}")
            
            # Play audio response
            if result.get("audio_response") is not None:
                print("🔊 Playing audio response...")
                chatbot.play_response(result)
                
                # Optionally save the response
                chatbot.save_response_audio(result, "demo_response.wav")
                print("💾 Audio response saved to demo_response.wav")


async def demo_emotion_awareness():
    """Demonstrate emotion-aware responses."""
    print("😊 Demo: Emotion-Aware Responses")
    print("=" * 50)
    
    config = ChatbotConfig()
    
    async with MultiModalChatbot(config) as chatbot:
        # Simulate different emotional contexts
        test_cases = [
            ("I'm feeling really happy today!", "happy"),
            ("I'm so frustrated with this problem", "angry"),
            ("I'm feeling a bit down lately", "sad"),
            ("Just a normal day, nothing special", "neutral")
        ]
        
        for message, expected_emotion in test_cases:
            print(f"👤 User: {message}")
            
            result = chatbot.chat_text(message)
            
            if result.get("error"):
                print(f"❌ Error: {result['error']}")
            else:
                detected_emotion = result.get("dominant_emotion", "neutral")
                response = result.get("text_response", "")
                
                print(f"😊 Expected emotion: {expected_emotion}")
                print(f"🎯 Detected emotion: {detected_emotion}")
                print(f"🤖 Response: {response}")
            
            print()


async def demo_conversation_history():
    """Demonstrate conversation history and context."""
    print("📚 Demo: Conversation History")
    print("=" * 50)
    
    config = ChatbotConfig()
    
    async with MultiModalChatbot(config) as chatbot:
        # Have a multi-turn conversation
        conversation = [
            "My name is Alice",
            "What's my name?",
            "I like pizza",
            "What food do I like?",
            "I'm planning a trip to Japan",
            "Where am I planning to go?"
        ]
        
        for message in conversation:
            print(f"👤 User: {message}")
            
            result = chatbot.chat_text(message)
            
            if result.get("error"):
                print(f"❌ Error: {result['error']}")
            else:
                response = result.get("text_response", "")
                print(f"🤖 Bot: {response}")
            
            print()
        
        # Show conversation statistics
        stats = chatbot.get_stats()
        print("📊 Conversation Statistics:")
        print(f"   Total turns: {stats['total_turns']}")
        print(f"   Emotion distribution: {stats['emotion_distribution']}")
        print(f"   Average response length: {stats['average_response_length']:.1f} characters")


async def demo_audio_file_processing():
    """Demonstrate processing audio files."""
    print("📁 Demo: Audio File Processing")
    print("=" * 50)
    
    config = ChatbotConfig()
    
    # Note: This demo requires an audio file
    audio_file = "sample_audio.wav"  # You would need to provide this
    
    if Path(audio_file).exists():
        async with MultiModalChatbot(config) as chatbot:
            print(f"🎵 Processing audio file: {audio_file}")
            
            result = chatbot.chat_voice(audio_file=audio_file, record_live=False)
            
            if result.get("error"):
                print(f"❌ Error: {result['error']}")
            else:
                transcription = result.get("transcribed_text", "")
                emotion = result.get("dominant_emotion", "neutral")
                response = result.get("text_response", "")
                
                print(f"🎤 Transcription: {transcription}")
                print(f"😊 Detected emotion: {emotion}")
                print(f"🤖 Response: {response}")
    else:
        print(f"⚠️ Audio file {audio_file} not found. Skipping this demo.")


async def main():
    """Run all demos."""
    print("🚀 HMM Chatbot Demo")
    print("=" * 50)
    print()
    
    demos = [
        ("Text Chat", demo_text_chat),
        ("Voice to Text", demo_voice_to_text),
        ("Full Voice Chat", demo_full_voice_chat),
        ("Emotion Awareness", demo_emotion_awareness),
        ("Conversation History", demo_conversation_history),
        ("Audio File Processing", demo_audio_file_processing)
    ]
    
    for name, demo_func in demos:
        try:
            print(f"\n🎯 Running {name} Demo...")
            await demo_func()
            print(f"✅ {name} Demo completed!")
        except KeyboardInterrupt:
            print(f"\n⏹️ {name} Demo interrupted by user")
            break
        except Exception as e:
            print(f"❌ {name} Demo failed: {e}")
        
        print("\n" + "=" * 50)
    
    print("\n🎉 All demos completed!")


if __name__ == "__main__":
    asyncio.run(main())
