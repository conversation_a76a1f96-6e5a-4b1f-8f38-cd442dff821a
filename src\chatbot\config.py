"""Configuration settings for the multi-modal chatbot."""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class ModelConfig:
    """Configuration for individual models."""
    model_name: str
    device: str = "auto"
    cache_dir: Optional[str] = None
    load_in_8bit: bool = False
    load_in_4bit: bool = False
    torch_dtype: str = "auto"
    trust_remote_code: bool = False


@dataclass
class AudioConfig:
    """Configuration for audio processing."""
    sample_rate: int = 16000
    chunk_size: int = 1024
    channels: int = 1
    format: str = "wav"
    max_duration: int = 30  # seconds
    silence_threshold: float = 0.01
    silence_duration: float = 2.0  # seconds
    save_audio: bool = False
    audio_save_dir: str = "audio_logs"


@dataclass
class ChatbotConfig:
    """Main configuration for the chatbot."""
    
    # Model configurations
    llm_config: ModelConfig = field(default_factory=lambda: ModelConfig(
        model_name="meta-llama/Meta-Llama-3.1-8B-Instruct",
        device="auto",
        load_in_8bit=True,
        trust_remote_code=True
    ))
    
    stt_config: ModelConfig = field(default_factory=lambda: ModelConfig(
        model_name="openai/whisper-large-v3",
        device="auto"
    ))
    
    emotion_config: ModelConfig = field(default_factory=lambda: ModelConfig(
        model_name="speechbrain/emotion-recognition-wav2vec2-IEMOCAP",
        device="auto"
    ))
    
    tts_config: ModelConfig = field(default_factory=lambda: ModelConfig(
        model_name="parler-tts/parler-tts-mini-v0.1",
        device="auto"
    ))
    
    # Audio configuration
    audio_config: AudioConfig = field(default_factory=AudioConfig)
    
    # General settings
    max_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    do_sample: bool = True
    
    # Conversation settings
    max_history_length: int = 10
    system_prompt: str = (
        "You are a helpful, friendly, and empathetic AI assistant. "
        "Respond naturally and conversationally to any topic. "
        "Consider the user's emotional state when crafting your responses. "
        "Be concise but informative, and maintain a warm, human-like tone."
    )
    
    # Cache and storage
    cache_dir: str = field(default_factory=lambda: str(Path.home() / ".cache" / "hmm-chatbot"))
    enable_caching: bool = True
    
    # API settings
    api_host: str = "localhost"
    api_port: int = 8000
    enable_api: bool = False
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    def __post_init__(self):
        """Post-initialization setup."""
        # Create cache directory if it doesn't exist
        if self.enable_caching:
            os.makedirs(self.cache_dir, exist_ok=True)
        
        # Set cache directories for models
        for config in [self.llm_config, self.stt_config, self.emotion_config, self.tts_config]:
            if config.cache_dir is None:
                config.cache_dir = os.path.join(self.cache_dir, "models")
    
    @classmethod
    def from_env(cls) -> "ChatbotConfig":
        """Create configuration from environment variables."""
        config = cls()
        
        # Override with environment variables if present
        if "HMM_LLM_MODEL" in os.environ:
            config.llm_config.model_name = os.environ["HMM_LLM_MODEL"]
        
        if "HMM_STT_MODEL" in os.environ:
            config.stt_config.model_name = os.environ["HMM_STT_MODEL"]
        
        if "HMM_EMOTION_MODEL" in os.environ:
            config.emotion_config.model_name = os.environ["HMM_EMOTION_MODEL"]
        
        if "HMM_TTS_MODEL" in os.environ:
            config.tts_config.model_name = os.environ["HMM_TTS_MODEL"]
        
        if "HMM_DEVICE" in os.environ:
            device = os.environ["HMM_DEVICE"]
            config.llm_config.device = device
            config.stt_config.device = device
            config.emotion_config.device = device
            config.tts_config.device = device
        
        if "HMM_CACHE_DIR" in os.environ:
            config.cache_dir = os.environ["HMM_CACHE_DIR"]
        
        if "HMM_API_HOST" in os.environ:
            config.api_host = os.environ["HMM_API_HOST"]
        
        if "HMM_API_PORT" in os.environ:
            config.api_port = int(os.environ["HMM_API_PORT"])
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "llm_config": self.llm_config.__dict__,
            "stt_config": self.stt_config.__dict__,
            "emotion_config": self.emotion_config.__dict__,
            "tts_config": self.tts_config.__dict__,
            "audio_config": self.audio_config.__dict__,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "do_sample": self.do_sample,
            "max_history_length": self.max_history_length,
            "system_prompt": self.system_prompt,
            "cache_dir": self.cache_dir,
            "enable_caching": self.enable_caching,
            "api_host": self.api_host,
            "api_port": self.api_port,
            "enable_api": self.enable_api,
            "log_level": self.log_level,
            "log_file": self.log_file
        }
