"""Main chatbot class integrating all components."""

import logging
import uuid
import asyncio
from typing import Dict, Any, Optional, Union, Callable
import numpy as np
from pathlib import Path

from .config import ChatbotConfig
from .models import ModelManager
from .audio_handler import AudioProcessor
from .graph import ChatbotGraph

logger = logging.getLogger(__name__)


class MultiModalChatbot:
    """Main multi-modal chatbot class."""
    
    def __init__(self, config: Optional[ChatbotConfig] = None):
        """Initialize the chatbot."""
        self.config = config or ChatbotConfig.from_env()
        self.conversation_id = str(uuid.uuid4())
        
        # Initialize components
        self.model_manager = ModelManager(self.config)
        self.audio_processor = AudioProcessor(self.config.audio_config)
        self.graph = None
        
        # State
        self._initialized = False
        self._models_loaded = False
        
        # Setup logging
        self._setup_logging()
        
        logger.info(f"Chatbot initialized with conversation ID: {self.conversation_id}")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.config.log_level.upper())
        
        # Configure root logger
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                *([logging.FileHandler(self.config.log_file)] if self.config.log_file else [])
            ]
        )
    
    async def initialize(self):
        """Initialize the chatbot asynchronously."""
        if self._initialized:
            return
        
        logger.info("Initializing chatbot...")
        
        try:
            # Load models
            await self._load_models()
            
            # Initialize graph
            self.graph = ChatbotGraph(
                self.config, 
                self.model_manager, 
                self.audio_processor
            )
            
            self._initialized = True
            logger.info("Chatbot initialization completed")
            
        except Exception as e:
            logger.error(f"Error initializing chatbot: {e}")
            raise
    
    async def _load_models(self):
        """Load all models asynchronously."""
        if self._models_loaded:
            return
        
        logger.info("Loading models...")
        
        # Run model loading in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self.model_manager.load_all)
        
        self._models_loaded = True
        logger.info("Models loaded successfully")
    
    def chat_text(self, message: str) -> Dict[str, Any]:
        """Chat with text input only."""
        if not self._initialized:
            raise RuntimeError("Chatbot not initialized. Call initialize() first.")
        
        logger.info(f"Processing text message: {message}")
        
        result = self.graph.process_turn(user_input=message, conversation_id=self.conversation_id)
        
        return result
    
    def chat_voice(self, audio_file: Optional[str] = None, 
                   record_live: bool = True) -> Dict[str, Any]:
        """Chat with voice input."""
        if not self._initialized:
            raise RuntimeError("Chatbot not initialized. Call initialize() first.")
        
        logger.info("Processing voice input...")
        
        try:
            if audio_file:
                # Load audio from file
                audio_data, sample_rate = self.audio_processor.load_audio_file(audio_file)
                logger.info(f"Loaded audio from file: {audio_file}")
            elif record_live:
                # Record live audio
                logger.info("Recording live audio...")
                audio_data = self.audio_processor.record_until_silence()
                sample_rate = self.config.audio_config.sample_rate
            else:
                raise ValueError("Either audio_file or record_live must be specified")
            
            if len(audio_data) == 0:
                return {
                    "error": "No audio data captured",
                    "text_response": "I didn't hear anything. Could you please try again?",
                    "audio_response": None
                }
            
            # Process through graph
            result = self.graph.process_turn(
                audio_input=audio_data,
                audio_sample_rate=sample_rate,
                conversation_id=self.conversation_id
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing voice input: {e}")
            return {
                "error": str(e),
                "text_response": "I encountered an error processing your voice input. Please try again.",
                "audio_response": None
            }
    
    def chat_multimodal(self, message: Optional[str] = None, 
                       audio_file: Optional[str] = None,
                       record_live: bool = False) -> Dict[str, Any]:
        """Chat with both text and voice capabilities."""
        if message:
            return self.chat_text(message)
        else:
            return self.chat_voice(audio_file=audio_file, record_live=record_live)
    
    def play_response(self, result: Dict[str, Any]) -> None:
        """Play the audio response from a chat result."""
        if result.get("audio_response") is not None:
            audio_data = result["audio_response"]
            sample_rate = result.get("audio_sample_rate", self.config.audio_config.sample_rate)
            
            self.audio_processor.play_response(audio_data, sample_rate)
        else:
            logger.warning("No audio response to play")
    
    def save_response_audio(self, result: Dict[str, Any], filepath: str) -> None:
        """Save the audio response to a file."""
        if result.get("audio_response") is not None:
            audio_data = result["audio_response"]
            sample_rate = result.get("audio_sample_rate", self.config.audio_config.sample_rate)
            
            self.audio_processor.player.save_audio(audio_data, sample_rate, filepath)
            logger.info(f"Audio response saved to: {filepath}")
        else:
            logger.warning("No audio response to save")
    
    def get_conversation_history(self) -> list:
        """Get the conversation history."""
        if self.graph:
            return [
                {
                    "turn_id": turn.turn_id,
                    "user_input": turn.user_input,
                    "bot_response": turn.bot_response,
                    "user_emotion": turn.user_emotion,
                    "dominant_emotion": turn.dominant_emotion
                }
                for turn in self.graph.conversation_history
            ]
        return []
    
    def clear_conversation(self) -> None:
        """Clear the conversation history."""
        if self.graph:
            self.graph.clear_history()
        
        # Generate new conversation ID
        self.conversation_id = str(uuid.uuid4())
        logger.info(f"Conversation cleared. New ID: {self.conversation_id}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get chatbot statistics."""
        history = self.get_conversation_history()
        
        if not history:
            return {
                "total_turns": 0,
                "emotion_distribution": {},
                "average_response_length": 0
            }
        
        # Calculate emotion distribution
        emotions = [turn["dominant_emotion"] for turn in history]
        emotion_counts = {}
        for emotion in emotions:
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        # Calculate average response length
        response_lengths = [len(turn["bot_response"]) for turn in history]
        avg_length = sum(response_lengths) / len(response_lengths) if response_lengths else 0
        
        return {
            "total_turns": len(history),
            "emotion_distribution": emotion_counts,
            "average_response_length": avg_length,
            "conversation_id": self.conversation_id,
            "models_loaded": self._models_loaded,
            "initialized": self._initialized
        }
    
    def update_config(self, **kwargs) -> None:
        """Update configuration parameters."""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"Updated config: {key} = {value}")
            else:
                logger.warning(f"Unknown config parameter: {key}")
    
    async def shutdown(self) -> None:
        """Shutdown the chatbot and cleanup resources."""
        logger.info("Shutting down chatbot...")
        
        try:
            # Unload models to free memory
            if self._models_loaded:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.model_manager.unload_all
                )
                self._models_loaded = False
            
            # Clear conversation history
            if self.graph:
                self.graph.clear_history()
            
            self._initialized = False
            logger.info("Chatbot shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        asyncio.run(self.shutdown())
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.shutdown()


# Convenience functions for quick usage
async def create_chatbot(config: Optional[ChatbotConfig] = None) -> MultiModalChatbot:
    """Create and initialize a chatbot."""
    chatbot = MultiModalChatbot(config)
    await chatbot.initialize()
    return chatbot


def quick_chat_text(message: str, config: Optional[ChatbotConfig] = None) -> str:
    """Quick text chat without maintaining state."""
    async def _chat():
        async with MultiModalChatbot(config) as chatbot:
            result = chatbot.chat_text(message)
            return result.get("text_response", "")
    
    return asyncio.run(_chat())


def quick_chat_voice(audio_file: str = None, record_live: bool = True, 
                    config: Optional[ChatbotConfig] = None) -> Dict[str, Any]:
    """Quick voice chat without maintaining state."""
    async def _chat():
        async with MultiModalChatbot(config) as chatbot:
            result = chatbot.chat_voice(audio_file=audio_file, record_live=record_live)
            return result
    
    return asyncio.run(_chat())
